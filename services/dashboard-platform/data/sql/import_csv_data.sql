-- =====================================================
-- CSV Data Import Script for User Requests
-- =====================================================

-- This script helps import data from the user_requests(jobs).csv file
-- Run this AFTER creating the schema with user_requests_schema.sql

-- =====================================================
-- TEMPORARY TABLE FOR CSV IMPORT
-- =====================================================

-- Create temporary table matching CSV structure
CREATE TEMP TABLE temp_csv_import (
    stt TEXT,
    request_code TEXT,
    empty_col TEXT, -- Empty column in CSV
    request_title TEXT,
    request_type TEXT,
    priority_level TEXT,
    status TEXT,
    source TEXT,
    location_code TEXT,
    request_date TEXT,
    expected_start_date TEXT,
    expected_end_date TEXT,
    actual_start_date TEXT,
    actual_end_date TEXT,
    executing_department TEXT,
    assigned_staff TEXT,
    request_content TEXT,
    service_category TEXT,
    service_score TEXT,
    nes_collected TEXT,
    rating_sent TEXT,
    processing_result TEXT,
    result_description TEXT
    -- Note: CSV has many empty columns at the end, we'll ignore them
);

-- =====================================================
-- IMPORT CSV DATA
-- =====================================================

-- Copy data from CSV file (adjust path as needed)
-- COPY temp_csv_import FROM '/path/to/your/user_requests(jobs).csv' 
-- WITH (FORMAT csv, HEADER true, DELIMITER ';', ENCODING 'UTF8');

-- Alternative: Use \copy command in psql
-- \copy temp_csv_import FROM 'services/dashboard-platform/data/user_requests(jobs).csv' WITH (FORMAT csv, HEADER true, DELIMITER ';', ENCODING 'UTF8');

-- =====================================================
-- DATA TRANSFORMATION AND INSERTION
-- =====================================================

-- Function to safely convert text to integer
CREATE OR REPLACE FUNCTION safe_int(input_text TEXT) 
RETURNS INTEGER AS $$
BEGIN
    IF input_text IS NULL OR TRIM(input_text) = '' THEN
        RETURN NULL;
    END IF;
    RETURN input_text::INTEGER;
EXCEPTION WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to safely convert text to timestamp
CREATE OR REPLACE FUNCTION safe_timestamp(input_text TEXT) 
RETURNS TIMESTAMP AS $$
BEGIN
    IF input_text IS NULL OR TRIM(input_text) = '' THEN
        RETURN NULL;
    END IF;
    -- Try different date formats
    BEGIN
        RETURN input_text::TIMESTAMP;
    EXCEPTION WHEN OTHERS THEN
        BEGIN
            RETURN TO_TIMESTAMP(input_text, 'DD/MM/YYYY HH24:MI:SS');
        EXCEPTION WHEN OTHERS THEN
            RETURN NULL;
        END;
    END;
END;
$$ LANGUAGE plpgsql;

-- Function to get or create lookup table IDs
CREATE OR REPLACE FUNCTION get_or_create_request_type_id(type_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    type_id INTEGER;
BEGIN
    IF type_name IS NULL OR TRIM(type_name) = '' THEN
        RETURN NULL;
    END IF;
    
    SELECT id INTO type_id FROM request_types WHERE type_name_vi = TRIM(type_name);
    
    IF type_id IS NULL THEN
        INSERT INTO request_types (type_code, type_name_vi, type_name_en)
        VALUES (UPPER(REPLACE(TRIM(type_name), ' ', '_')), TRIM(type_name), TRIM(type_name))
        RETURNING id INTO type_id;
    END IF;
    
    RETURN type_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_or_create_priority_id(priority_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    priority_id INTEGER;
BEGIN
    IF priority_name IS NULL OR TRIM(priority_name) = '' THEN
        RETURN NULL;
    END IF;
    
    SELECT id INTO priority_id FROM priority_levels WHERE priority_name_vi = TRIM(priority_name);
    
    IF priority_id IS NULL THEN
        INSERT INTO priority_levels (priority_code, priority_name_vi, priority_name_en, priority_order)
        VALUES (UPPER(REPLACE(TRIM(priority_name), ' ', '_')), TRIM(priority_name), TRIM(priority_name), 
                (SELECT COALESCE(MAX(priority_order), 0) + 1 FROM priority_levels))
        RETURNING id INTO priority_id;
    END IF;
    
    RETURN priority_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_or_create_status_id(status_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    status_id INTEGER;
    is_final BOOLEAN;
BEGIN
    IF status_name IS NULL OR TRIM(status_name) = '' THEN
        RETURN NULL;
    END IF;
    
    SELECT id INTO status_id FROM request_statuses WHERE status_name_vi = TRIM(status_name);
    
    IF status_id IS NULL THEN
        -- Determine if this is a final status
        is_final := TRIM(status_name) IN ('Đã xử lý', 'Hoàn thành', 'Hủy bỏ');
        
        INSERT INTO request_statuses (status_code, status_name_vi, status_name_en, status_order, is_final_status)
        VALUES (UPPER(REPLACE(TRIM(status_name), ' ', '_')), TRIM(status_name), TRIM(status_name),
                (SELECT COALESCE(MAX(status_order), 0) + 1 FROM request_statuses), is_final)
        RETURNING id INTO status_id;
    END IF;
    
    RETURN status_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_or_create_source_id(source_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    source_id INTEGER;
BEGIN
    IF source_name IS NULL OR TRIM(source_name) = '' THEN
        RETURN NULL;
    END IF;
    
    SELECT id INTO source_id FROM request_sources WHERE source_name_vi = TRIM(source_name);
    
    IF source_id IS NULL THEN
        INSERT INTO request_sources (source_code, source_name_vi, source_name_en)
        VALUES (UPPER(REPLACE(TRIM(source_name), ' ', '_')), TRIM(source_name), TRIM(source_name))
        RETURNING id INTO source_id;
    END IF;
    
    RETURN source_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_or_create_department_id(dept_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    dept_id INTEGER;
BEGIN
    IF dept_name IS NULL OR TRIM(dept_name) = '' THEN
        RETURN NULL;
    END IF;
    
    SELECT id INTO dept_id FROM departments WHERE department_name_vi = TRIM(dept_name);
    
    IF dept_id IS NULL THEN
        INSERT INTO departments (department_code, department_name_vi, department_name_en)
        VALUES (UPPER(REPLACE(TRIM(dept_name), ' ', '_')), TRIM(dept_name), TRIM(dept_name))
        RETURNING id INTO dept_id;
    END IF;
    
    RETURN dept_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_or_create_service_category_id(category_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    category_id INTEGER;
BEGIN
    IF category_name IS NULL OR TRIM(category_name) = '' THEN
        RETURN NULL;
    END IF;
    
    SELECT id INTO category_id FROM service_categories WHERE category_name_vi = TRIM(category_name);
    
    IF category_id IS NULL THEN
        INSERT INTO service_categories (category_code, category_name_vi, category_name_en)
        VALUES (UPPER(REPLACE(TRIM(category_name), ' ', '_')), TRIM(category_name), TRIM(category_name))
        RETURNING id INTO category_id;
    END IF;
    
    RETURN category_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INSERT TRANSFORMED DATA
-- =====================================================

-- Insert data from temp table to main table
INSERT INTO user_requests (
    stt,
    request_code,
    request_title,
    request_type_id,
    priority_id,
    status_id,
    source_id,
    location_code,
    request_date,
    expected_start_date,
    expected_end_date,
    actual_start_date,
    actual_end_date,
    executing_department_id,
    assigned_staff,
    request_content,
    service_category_id,
    service_score,
    nes_collected,
    rating_sent,
    processing_result,
    result_description
)
SELECT 
    safe_int(stt),
    NULLIF(TRIM(request_code), ''),
    NULLIF(TRIM(request_title), ''),
    get_or_create_request_type_id(request_type),
    get_or_create_priority_id(priority_level),
    get_or_create_status_id(status),
    get_or_create_source_id(source),
    NULLIF(TRIM(location_code), ''),
    safe_timestamp(request_date),
    safe_timestamp(expected_start_date),
    safe_timestamp(expected_end_date),
    safe_timestamp(actual_start_date),
    safe_timestamp(actual_end_date),
    get_or_create_department_id(executing_department),
    NULLIF(TRIM(assigned_staff), ''),
    NULLIF(TRIM(request_content), ''),
    get_or_create_service_category_id(service_category),
    safe_int(service_score),
    NULLIF(TRIM(nes_collected), ''),
    NULLIF(TRIM(rating_sent), ''),
    NULLIF(TRIM(processing_result), ''),
    NULLIF(TRIM(result_description), '')
FROM temp_csv_import
WHERE stt IS NOT NULL AND TRIM(stt) != '' AND TRIM(stt) != 'STT';

-- =====================================================
-- CLEANUP
-- =====================================================

-- Drop temporary functions
DROP FUNCTION IF EXISTS safe_int(TEXT);
DROP FUNCTION IF EXISTS safe_timestamp(TEXT);
DROP FUNCTION IF EXISTS get_or_create_request_type_id(TEXT);
DROP FUNCTION IF EXISTS get_or_create_priority_id(TEXT);
DROP FUNCTION IF EXISTS get_or_create_status_id(TEXT);
DROP FUNCTION IF EXISTS get_or_create_source_id(TEXT);
DROP FUNCTION IF EXISTS get_or_create_department_id(TEXT);
DROP FUNCTION IF EXISTS get_or_create_service_category_id(TEXT);

-- Show import results
SELECT 
    'Import completed successfully!' as message,
    COUNT(*) as total_records_imported
FROM user_requests;

-- Show summary by status
SELECT 
    rs.status_name_vi as status,
    COUNT(*) as count
FROM user_requests ur
JOIN request_statuses rs ON ur.status_id = rs.id
GROUP BY rs.status_name_vi, rs.status_order
ORDER BY rs.status_order;
