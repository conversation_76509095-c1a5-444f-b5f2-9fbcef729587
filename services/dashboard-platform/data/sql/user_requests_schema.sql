-- =====================================================
-- SQL Schema for User Requests (Jobs) System
-- Generated from: user_requests(jobs).csv
-- Description: Service request and job tracking system for Vinhomes residential management
-- =====================================================

-- Drop tables if they exist (for clean recreation)
DROP TABLE IF EXISTS user_requests;
DROP TABLE IF EXISTS request_types;
DROP TABLE IF EXISTS priority_levels;
DROP TABLE IF EXISTS request_statuses;
DROP TABLE IF EXISTS request_sources;
DROP TABLE IF EXISTS departments;
DROP TABLE IF EXISTS service_categories;

-- =====================================================
-- LOOKUP TABLES (Reference Data)
-- =====================================================

-- Request Types lookup table
CREATE TABLE request_types (
    id SERIAL PRIMARY KEY,
    type_code VARCHAR(50) UNIQUE NOT NULL,
    type_name_vi VARCHAR(200) NOT NULL,
    type_name_en VARCHAR(200),
    description TEXT,
    is_active BIT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Priority Levels lookup table
CREATE TABLE priority_levels (
    id SERIAL PRIMARY KEY,
    priority_code VARCHAR(20) UNIQUE NOT NULL,
    priority_name_vi VARCHAR(100) NOT NULL,
    priority_name_en VARCHAR(100),
    priority_order INTEGER NOT NULL,
    color_code VARCHAR(7), -- For UI display (hex color)
    is_active BIT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Request Statuses lookup table
CREATE TABLE request_statuses (
    id SERIAL PRIMARY KEY,
    status_code VARCHAR(50) UNIQUE NOT NULL,
    status_name_vi VARCHAR(100) NOT NULL,
    status_name_en VARCHAR(100),
    status_order INTEGER NOT NULL,
    is_final_status BIT NOT NULL DEFAULT 0,
    color_code VARCHAR(7), -- For UI display
    is_active BIT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Request Sources lookup table
CREATE TABLE request_sources (
    id SERIAL PRIMARY KEY,
    source_code VARCHAR(50) UNIQUE NOT NULL,
    source_name_vi VARCHAR(100) NOT NULL,
    source_name_en VARCHAR(100),
    description TEXT,
    is_active BIT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Departments lookup table
CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    department_code VARCHAR(50) UNIQUE NOT NULL,
    department_name_vi VARCHAR(200) NOT NULL,
    department_name_en VARCHAR(200),
    parent_department_id INTEGER REFERENCES departments(id),
    is_active BIT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Service Categories lookup table
CREATE TABLE service_categories (
    id SERIAL PRIMARY KEY,
    category_code VARCHAR(50) UNIQUE NOT NULL,
    category_name_vi VARCHAR(200) NOT NULL,
    category_name_en VARCHAR(200),
    description TEXT,
    is_active BIT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- MAIN TABLE
-- =====================================================

-- Main User Requests table
CREATE TABLE user_requests (
    -- Primary identification
    id SERIAL PRIMARY KEY,
    stt INTEGER, -- Sequential number from CSV
    request_code VARCHAR(100) UNIQUE, -- Mã
    
    -- Request details
    request_title VARCHAR(500), -- Tên phản ánh
    request_type_id INTEGER REFERENCES request_types(id),
    priority_id INTEGER REFERENCES priority_levels(id),
    status_id INTEGER REFERENCES request_statuses(id),
    source_id INTEGER REFERENCES request_sources(id),
    
    -- Location and contact
    location_code VARCHAR(100), -- Vị trí (apartment/unit code)
    
    -- Timestamps
    request_date TIMESTAMP, -- Ngày phản ánh
    expected_start_date TIMESTAMP, -- Bắt đầu dự kiến
    expected_end_date TIMESTAMP, -- Kết thúc dự kiến
    actual_start_date TIMESTAMP, -- Bắt đầu thực tế
    actual_end_date TIMESTAMP, -- Kết thúc thực tế
    
    -- Assignment and execution
    executing_department_id INTEGER REFERENCES departments(id), -- Đơn vị thực hiện
    assigned_staff TEXT, -- Người thực hiện (can be multiple people)
    
    -- Content and details
    request_content TEXT, -- Nội dung
    service_category_id INTEGER REFERENCES service_categories(id), -- Mảng dịch vụ
    
    -- Scoring and feedback
    service_score INTEGER CHECK (service_score >= 0 AND service_score <= 10), -- Điểm đầu việc
    nes_collected VARCHAR(50), -- Lấy NES (Yes/No/Unknown)
    rating_sent VARCHAR(50), -- Đã gửi Rating (Yes/No/Pending)
    
    -- Results
    processing_result TEXT, -- Kết quả xử lý
    result_description TEXT, -- Mô tả kết quả
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    
    -- Additional metadata
    is_deleted BIT DEFAULT 0,
    deletion_reason TEXT,
    deleted_at TIMESTAMP,
    deleted_by VARCHAR(100)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Primary search indexes
CREATE INDEX idx_user_requests_request_code ON user_requests(request_code);
CREATE INDEX idx_user_requests_location_code ON user_requests(location_code);
CREATE INDEX idx_user_requests_request_date ON user_requests(request_date);
CREATE INDEX idx_user_requests_status ON user_requests(status_id);
CREATE INDEX idx_user_requests_priority ON user_requests(priority_id);
CREATE INDEX idx_user_requests_department ON user_requests(executing_department_id);
CREATE INDEX idx_user_requests_source ON user_requests(source_id);

-- Composite indexes for common queries
CREATE INDEX idx_user_requests_status_date ON user_requests(status_id, request_date);
CREATE INDEX idx_user_requests_dept_status ON user_requests(executing_department_id, status_id);
CREATE INDEX idx_user_requests_location_date ON user_requests(location_code, request_date);

-- Full-text search indexes
CREATE INDEX idx_user_requests_title_fts ON user_requests USING gin(to_tsvector('english', request_title));
CREATE INDEX idx_user_requests_content_fts ON user_requests USING gin(to_tsvector('english', request_content));

-- =====================================================
-- TRIGGERS FOR AUDIT TRAIL
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_user_requests_updated_at 
    BEFORE UPDATE ON user_requests 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- Comprehensive view with all lookup data joined
CREATE VIEW vw_user_requests_detailed AS
SELECT 
    ur.id,
    ur.stt,
    ur.request_code,
    ur.request_title,
    rt.type_name_vi as request_type,
    pl.priority_name_vi as priority_level,
    rs.status_name_vi as status,
    src.source_name_vi as source,
    ur.location_code,
    ur.request_date,
    ur.expected_start_date,
    ur.expected_end_date,
    ur.actual_start_date,
    ur.actual_end_date,
    dept.department_name_vi as executing_department,
    ur.assigned_staff,
    ur.request_content,
    sc.category_name_vi as service_category,
    ur.service_score,
    ur.nes_collected,
    ur.rating_sent,
    ur.processing_result,
    ur.result_description,
    ur.created_at,
    ur.updated_at
FROM user_requests ur
LEFT JOIN request_types rt ON ur.request_type_id = rt.id
LEFT JOIN priority_levels pl ON ur.priority_id = pl.id
LEFT JOIN request_statuses rs ON ur.status_id = rs.id
LEFT JOIN request_sources src ON ur.source_id = src.id
LEFT JOIN departments dept ON ur.executing_department_id = dept.id
LEFT JOIN service_categories sc ON ur.service_category_id = sc.id
WHERE ur.is_deleted = FALSE;

-- Summary statistics view
CREATE VIEW vw_request_statistics AS
SELECT 
    COUNT(*) as total_requests,
    COUNT(CASE WHEN rs.is_final_status = TRUE THEN 1 END) as completed_requests,
    COUNT(CASE WHEN rs.is_final_status = FALSE THEN 1 END) as pending_requests,
    AVG(ur.service_score) as average_service_score,
    AVG(EXTRACT(EPOCH FROM (ur.actual_end_date - ur.actual_start_date))/3600) as avg_completion_hours
FROM user_requests ur
LEFT JOIN request_statuses rs ON ur.status_id = rs.id
WHERE ur.is_deleted = FALSE;

-- =====================================================
-- SAMPLE REFERENCE DATA
-- =====================================================

-- Insert sample request types
INSERT INTO request_types (type_code, type_name_vi, type_name_en) VALUES
('CARD_ISSUE', 'Cấp phát thẻ', 'Card Issuance'),
('CARD_CANCEL', 'Huỷ thẻ', 'Card Cancellation'),
('CARD_REISSUE', 'Cấp lại thẻ', 'Card Reissuance'),
('SERVICE_REQUEST', 'Yêu cầu dịch vụ', 'Service Request'),
('RESIDENT_FEEDBACK', 'Góp ý cư dân', 'Resident Feedback'),
('FACE_REGISTRATION', 'Đăng ký khuôn mặt', 'Face Registration');

-- Insert priority levels
INSERT INTO priority_levels (priority_code, priority_name_vi, priority_name_en, priority_order, color_code) VALUES
('LOW', 'Thấp', 'Low', 1, '#28a745'),
('MEDIUM', 'Trung bình', 'Medium', 2, '#ffc107'),
('HIGH', 'Cao', 'High', 3, '#fd7e14'),
('URGENT', 'Khẩn cấp', 'Urgent', 4, '#dc3545');

-- Insert request statuses
INSERT INTO request_statuses (status_code, status_name_vi, status_name_en, status_order, is_final_status, color_code) VALUES
('NOT_STARTED', 'Chưa thực hiện', 'Not Started', 1, FALSE, '#6c757d'),
('IN_PROGRESS', 'Đang thực hiện', 'In Progress', 2, FALSE, '#007bff'),
('PROCESSED', 'Đã xử lý', 'Processed', 3, TRUE, '#28a745'),
('COMPLETED', 'Hoàn thành', 'Completed', 4, TRUE, '#28a745'),
('CANCELLED', 'Hủy bỏ', 'Cancelled', 5, TRUE, '#dc3545');

-- Insert request sources
INSERT INTO request_sources (source_code, source_name_vi, source_name_en) VALUES
('RESIDENT_APP', 'App cư dân', 'Resident App'),
('CALL_CENTER', 'Tổng đài', 'Call Center'),
('FRONT_DESK', 'Tại quầy', 'Front Desk'),
('EMAIL', 'Email', 'Email'),
('WEBSITE', 'Website', 'Website');

-- Insert departments
INSERT INTO departments (department_code, department_name_vi, department_name_en) VALUES
('CUSTOMER_SERVICE', 'Bộ phận CSKH', 'Customer Service Department'),
('CUSTOMER_SERVICE_HIGH', 'CSKH cao tầng', 'High-rise Customer Service'),
('CUSTOMER_SERVICE_RECEPTION', 'CSKH Lễ tân', 'Reception Customer Service'),
('TECHNICAL_ME', 'Kỹ thuật ME cao tầng', 'High-rise ME Technical'),
('TECHNICAL_BM', 'Kỹ thuật BM cao tầng', 'High-rise BM Technical'),
('SECURITY_CAMERA', 'An ninh Camera', 'Security Camera'),
('TICC', 'Bộ phận TICC', 'TICC Department');

-- Insert service categories
INSERT INTO service_categories (category_code, category_name_vi, category_name_en) VALUES
('CUSTOMER_CARE', 'Dịch vụ Chăm sóc khách hàng', 'Customer Care Service'),
('TECHNICAL_SERVICE', 'Dịch vụ Kỹ thuật', 'Technical Service'),
('SECURITY_SERVICE', 'Dịch vụ An ninh bảo vệ', 'Security Service');

-- =====================================================
-- ADDITIONAL CONSTRAINTS AND BUSINESS RULES
-- =====================================================

-- Ensure actual dates are not before expected dates (when both exist)
ALTER TABLE user_requests ADD CONSTRAINT chk_actual_start_after_expected
    CHECK (actual_start_date IS NULL OR expected_start_date IS NULL OR actual_start_date >= expected_start_date - INTERVAL '1 day');

ALTER TABLE user_requests ADD CONSTRAINT chk_actual_end_after_start
    CHECK (actual_end_date IS NULL OR actual_start_date IS NULL OR actual_end_date >= actual_start_date);

ALTER TABLE user_requests ADD CONSTRAINT chk_expected_end_after_start
    CHECK (expected_end_date IS NULL OR expected_start_date IS NULL OR expected_end_date >= expected_start_date);

-- Ensure request_code is not empty when provided
ALTER TABLE user_requests ADD CONSTRAINT chk_request_code_not_empty
    CHECK (request_code IS NULL OR LENGTH(TRIM(request_code)) > 0);

-- Ensure location_code follows expected format (basic validation)
ALTER TABLE user_requests ADD CONSTRAINT chk_location_code_format
    CHECK (location_code IS NULL OR location_code ~ '^[A-Z0-9][A-Z0-9.]*[A-Z0-9]$');

-- =====================================================
-- FUNCTIONS FOR DATA ANALYSIS
-- =====================================================

-- Function to calculate request processing time in hours
CREATE OR REPLACE FUNCTION get_processing_time_hours(req_id INTEGER)
RETURNS NUMERIC AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
BEGIN
    SELECT actual_start_date, actual_end_date
    INTO start_time, end_time
    FROM user_requests
    WHERE id = req_id;

    IF start_time IS NULL OR end_time IS NULL THEN
        RETURN NULL;
    END IF;

    RETURN EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0;
END;
$$ LANGUAGE plpgsql;

-- Function to get overdue requests
CREATE OR REPLACE FUNCTION get_overdue_requests()
RETURNS TABLE (
    request_id INTEGER,
    request_code VARCHAR(100),
    request_title VARCHAR(500),
    expected_end_date TIMESTAMP,
    days_overdue INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ur.id,
        ur.request_code,
        ur.request_title,
        ur.expected_end_date,
        EXTRACT(DAY FROM (CURRENT_TIMESTAMP - ur.expected_end_date))::INTEGER
    FROM user_requests ur
    JOIN request_statuses rs ON ur.status_id = rs.id
    WHERE ur.expected_end_date < CURRENT_TIMESTAMP
      AND rs.is_final_status = FALSE
      AND ur.is_deleted = FALSE
    ORDER BY ur.expected_end_date ASC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE user_requests IS 'Main table storing all user service requests and job tracking information';
COMMENT ON COLUMN user_requests.stt IS 'Sequential number from original CSV file';
COMMENT ON COLUMN user_requests.request_code IS 'Unique identifier for the request (Mã)';
COMMENT ON COLUMN user_requests.location_code IS 'Apartment or unit code where service is requested';
COMMENT ON COLUMN user_requests.service_score IS 'Service quality score from 0-10';
COMMENT ON COLUMN user_requests.nes_collected IS 'Whether NES (Net Promoter Score) was collected';
COMMENT ON COLUMN user_requests.rating_sent IS 'Status of rating/feedback collection';

COMMENT ON VIEW vw_user_requests_detailed IS 'Comprehensive view with all related lookup data for reporting';
COMMENT ON VIEW vw_request_statistics IS 'Summary statistics for dashboard and reporting';

-- =====================================================
-- SAMPLE USAGE QUERIES
-- =====================================================

/*
-- Example queries to use with this schema:

-- 1. Get all pending requests with details
SELECT * FROM vw_user_requests_detailed
WHERE status IN ('Chưa thực hiện', 'Đang thực hiện')
ORDER BY request_date DESC;

-- 2. Get overdue requests
SELECT * FROM get_overdue_requests();

-- 3. Get processing time for completed requests
SELECT
    request_code,
    request_title,
    get_processing_time_hours(id) as processing_hours
FROM user_requests
WHERE actual_start_date IS NOT NULL AND actual_end_date IS NOT NULL;

-- 4. Department performance summary
SELECT
    dept.department_name_vi,
    COUNT(*) as total_requests,
    AVG(ur.service_score) as avg_score,
    AVG(get_processing_time_hours(ur.id)) as avg_processing_hours
FROM user_requests ur
JOIN departments dept ON ur.executing_department_id = dept.id
WHERE ur.is_deleted = FALSE
GROUP BY dept.department_name_vi
ORDER BY avg_score DESC;

-- 5. Monthly request trends
SELECT
    DATE_TRUNC('month', request_date) as month,
    COUNT(*) as request_count,
    AVG(service_score) as avg_score
FROM user_requests
WHERE request_date >= CURRENT_DATE - INTERVAL '12 months'
  AND is_deleted = FALSE
GROUP BY DATE_TRUNC('month', request_date)
ORDER BY month;
*/

-- =====================================================
-- GRANTS (Adjust based on your security requirements)
-- =====================================================

-- Example grants for different user roles
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
-- GRANT SELECT, INSERT, UPDATE ON user_requests TO service_staff;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO admin_user;

-- =====================================================
-- SCHEMA CREATION COMPLETE
-- =====================================================
