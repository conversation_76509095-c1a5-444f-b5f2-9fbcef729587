-- SQL Schema for user_requests_jobs table (SQL Server)
-- Based on the CSV file: user_requests(jobs).csv
-- Creates Vinhomes database and user_requests_jobs table

-- Create Vinhomes database
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'Vinhomes')
BEGIN
    CREATE DATABASE [Vinhomes]
    COLLATE SQL_Latin1_General_CP1_CI_AS;
END
GO

-- Use the Vinhomes database
USE [Vinhomes];
GO

-- Drop table if it exists (for re-running the script)
IF OBJECT_ID('dbo.user_requests_jobs', 'U') IS NOT NULL
    DROP TABLE dbo.user_requests_jobs;
GO

CREATE TABLE dbo.user_requests_jobs (
    -- Primary key and basic identification
    id INT IDENTITY(1,1) PRIMARY KEY,
    stt INT,                                       -- STT (Sequential number)
    ma NVARCHAR(100),                             -- Mã (Code/ID)
    empty_column NVARCHAR(10),                    -- Empty column (3rd column)

    -- Request details
    ten_phan_anh NVARCHAR(MAX),                   -- Tên phản ánh (Request title)
    loai_phan_anh NVARCHAR(100),                  -- <PERSON><PERSON><PERSON> ph<PERSON>nh (Request type)
    do_uu_tien NVARCHAR(50),                      -- Độ ưu tiên (Priority level)
    trang_thai NVARCHAR(100),                     -- Trạng thái (Status)
    nguon NVARCHAR(100),                          -- Nguồn (Source)
    vi_tri NVARCHAR(100),                         -- Vị trí (Location/Position)

    -- Date and time fields
    ngay_phan_anh DATETIME2,                      -- Ngày phản ánh (Request date)
    bat_dau_du_kien DATETIME2,                    -- Bắt đầu dự kiến (Expected start)
    ket_thuc_du_kien DATETIME2,                   -- Kết thúc dự kiến (Expected end)
    bat_dau_thuc_te DATETIME2,                    -- Bắt đầu thực tế (Actual start)
    ket_thuc_thuc_te DATETIME2,                   -- Kết thúc thực tế (Actual end)

    -- Assignment and execution
    don_vi_thuc_hien NVARCHAR(200),               -- Đơn vị thực hiện (Executing unit)
    nguoi_thuc_hien NVARCHAR(MAX),                -- Người thực hiện (Executor/Assignee)

    -- Content and details
    noi_dung NVARCHAR(MAX),                       -- Nội dung (Content/Description)
    mang_dich_vu NVARCHAR(200),                   -- Mảng dịch vụ (Service category)

    -- Additional tracking fields
    diem_dau_viec NVARCHAR(50),                   -- Điểm đầu việc (Work start point)
    lay_nes NVARCHAR(50),                         -- Lấy NES (NES collection)
    da_gui_rating NVARCHAR(50),                   -- Đã gửi Rating (Rating sent)
    ket_qua_xu_ly NVARCHAR(MAX),                  -- Kết quả xử lý (Processing result)
    mo_ta_ket_qua NVARCHAR(MAX),                  -- Mô tả kết quả (Result description)

    -- Metadata
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);
GO

-- Indexes for better performance
CREATE NONCLUSTERED INDEX IX_user_requests_ma ON user_requests_jobs(ma);
CREATE NONCLUSTERED INDEX IX_user_requests_trang_thai ON user_requests_jobs(trang_thai);
CREATE NONCLUSTERED INDEX IX_user_requests_loai_phan_anh ON user_requests_jobs(loai_phan_anh);
CREATE NONCLUSTERED INDEX IX_user_requests_ngay_phan_anh ON user_requests_jobs(ngay_phan_anh);
CREATE NONCLUSTERED INDEX IX_user_requests_vi_tri ON user_requests_jobs(vi_tri);
CREATE NONCLUSTERED INDEX IX_user_requests_don_vi_thuc_hien ON user_requests_jobs(don_vi_thuc_hien);
CREATE NONCLUSTERED INDEX IX_user_requests_do_uu_tien ON user_requests_jobs(do_uu_tien);
GO

-- Extended Properties for documentation (SQL Server equivalent of comments)
EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'User requests and jobs tracking system for Vinhomes Ocean Park',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs';

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Sequential number from CSV',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs',
    @level2type = N'COLUMN', @level2name = N'stt';

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Request/Job code identifier',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs',
    @level2type = N'COLUMN', @level2name = N'ma';

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Request title or name',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs',
    @level2type = N'COLUMN', @level2name = N'ten_phan_anh';

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Type of request (e.g., Cấp phát thẻ, Yêu cầu dịch vụ, Góp ý cư dân)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs',
    @level2type = N'COLUMN', @level2name = N'loai_phan_anh';

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Priority level (e.g., Trung bình, Cao, Thấp)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs',
    @level2type = N'COLUMN', @level2name = N'do_uu_tien';

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Current status (e.g., Đã xử lý, Đang thực hiện, Chưa thực hiện)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs',
    @level2type = N'COLUMN', @level2name = N'trang_thai';

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Source of request (e.g., App cư dân, Tổng đài, Tại quầy)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs',
    @level2type = N'COLUMN', @level2name = N'nguon';

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Location/apartment code (e.g., S1.081209, R1.05118A)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs',
    @level2type = N'COLUMN', @level2name = N'vi_tri';

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Detailed content and description of the request',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs',
    @level2type = N'COLUMN', @level2name = N'noi_dung';

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Service category (e.g., Dịch vụ Chăm sóc khách hàng, Dịch vụ Kỹ thuật)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'user_requests_jobs',
    @level2type = N'COLUMN', @level2name = N'mang_dich_vu';
GO

-- Script completed successfully
PRINT 'Vinhomes database and user_requests_jobs table created successfully!';