-- SQL Schema for user_requests_jobs table
-- Based on the CSV file: user_requests(jobs).csv

CREATE TABLE user_requests_jobs (
    -- Primary key and basic identification
    id SERIAL PRIMARY KEY,
    stt INTEGER,                                    -- STT (Sequential number)
    ma VARCHAR(100),                               -- <PERSON><PERSON> (Code/ID)
    empty_column VARCHAR(10),                      -- Empty column (3rd column)
    
    -- Request details
    ten_phan_anh TEXT,                             -- Tên ph<PERSON>n ánh (Request title)
    loai_phan_anh VARCHAR(100),                    -- Lo<PERSON><PERSON> phản ánh (Request type)
    do_uu_tien VARCHAR(50),                        -- <PERSON><PERSON> <PERSON>u tiên (Priority level)
    trang_thai VARCHAR(100),                       -- Tr<PERSON><PERSON> thái (Status)
    nguon VARCHAR(100),                            -- <PERSON><PERSON><PERSON><PERSON> (Source)
    vi_tri VARCHAR(100),                           -- <PERSON><PERSON> trí (Location/Position)
    
    -- Date and time fields
    ngay_phan_anh TIMESTAMP,                       -- <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> (Request date)
    bat_dau_du_kien TIMESTAMP,                     -- Bắt đầu dự kiến (Expected start)
    ket_thuc_du_kien TIMESTAMP,                    -- <PERSON><PERSON><PERSON> thú<PERSON> dự kiến (Expected end)
    bat_dau_thuc_te TIMESTAMP,                     -- Bắt đầu thực tế (Actual start)
    ket_thuc_thuc_te TIMESTAMP,                    -- Kết thúc thực tế (Actual end)
    
    -- Assignment and execution
    don_vi_thuc_hien VARCHAR(200),                 -- Đơn vị thực hiện (Executing unit)
    nguoi_thuc_hien TEXT,                          -- Người thực hiện (Executor/Assignee)
    
    -- Content and details
    noi_dung TEXT,                                 -- Nội dung (Content/Description)
    mang_dich_vu VARCHAR(200),                     -- Mảng dịch vụ (Service category)
    
    -- Additional tracking fields
    diem_dau_viec VARCHAR(50),                     -- Điểm đầu việc (Work start point)
    lay_nes VARCHAR(50),                           -- Lấy NES (NES collection)
    da_gui_rating VARCHAR(50),                     -- Đã gửi Rating (Rating sent)
    ket_qua_xu_ly TEXT,                           -- Kết quả xử lý (Processing result)
    mo_ta_ket_qua TEXT,                           -- Mô tả kết quả (Result description)
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
-- CREATE INDEX idx_user_requests_ma ON user_requests_jobs(ma);
-- CREATE INDEX idx_user_requests_trang_thai ON user_requests_jobs(trang_thai);
-- CREATE INDEX idx_user_requests_loai_phan_anh ON user_requests_jobs(loai_phan_anh);
-- CREATE INDEX idx_user_requests_ngay_phan_anh ON user_requests_jobs(ngay_phan_anh);
-- CREATE INDEX idx_user_requests_vi_tri ON user_requests_jobs(vi_tri);
-- CREATE INDEX idx_user_requests_don_vi_thuc_hien ON user_requests_jobs(don_vi_thuc_hien);
-- CREATE INDEX idx_user_requests_do_uu_tien ON user_requests_jobs(do_uu_tien);

-- Comments for documentation
-- COMMENT ON TABLE user_requests_jobs IS 'User requests and jobs tracking system for Vinhomes Ocean Park';
-- COMMENT ON COLUMN user_requests_jobs.stt IS 'Sequential number from CSV';
-- COMMENT ON COLUMN user_requests_jobs.ma IS 'Request/Job code identifier';
-- COMMENT ON COLUMN user_requests_jobs.ten_phan_anh IS 'Request title or name';
-- COMMENT ON COLUMN user_requests_jobs.loai_phan_anh IS 'Type of request (e.g., Cấp phát thẻ, Yêu cầu dịch vụ, Góp ý cư dân)';
-- COMMENT ON COLUMN user_requests_jobs.do_uu_tien IS 'Priority level (e.g., Trung bình, Cao, Thấp)';
-- COMMENT ON COLUMN user_requests_jobs.trang_thai IS 'Current status (e.g., Đã xử lý, Đang thực hiện, Chưa thực hiện)';
-- COMMENT ON COLUMN user_requests_jobs.nguon IS 'Source of request (e.g., App cư dân, Tổng đài, Tại quầy)';
-- COMMENT ON COLUMN user_requests_jobs.vi_tri IS 'Location/apartment code (e.g., S1.081209, R1.05118A)';
-- COMMENT ON COLUMN user_requests_jobs.noi_dung IS 'Detailed content and description of the request';
-- COMMENT ON COLUMN user_requests_jobs.mang_dich_vu IS 'Service category (e.g., Dịch vụ Chăm sóc khách hàng, Dịch vụ Kỹ thuật)';